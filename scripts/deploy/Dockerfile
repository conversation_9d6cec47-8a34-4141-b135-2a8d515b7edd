FROM node:22-slim AS builder

# --max-old-space-size
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV NODE_OPTIONS=--max-old-space-size=8192
ENV TZ=Asia/Shanghai

RUN npm i -g corepack

WORKDIR /app

# copy package.json and pnpm-lock.yaml to workspace
COPY . /app

# 安装依赖（忽略 lockfile 过期，避免 CI/远端构建失败）
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --no-frozen-lockfile
# 仅构建 web-antd 应用，避免无关包的构建
RUN pnpm run build --filter=@vben/web-antd

RUN echo "Builder Success 🎉"

# 使用轻量 Node 运行时 + serve 提供静态文件，不依赖 Nginx
FROM node:22-alpine AS production

ENV TZ=Asia/Shanghai \
    NODE_ENV=production

WORKDIR /app

# 安装静态资源服务器
RUN npm i -g serve@14

# 复制构建产物
COPY --from=builder /app/apps/web-antd/dist ./dist

# 暴露端口，可通过环境变量 PORT 覆盖
EXPOSE 8080

# 以 SPA 方式提供静态文件，绑定 0.0.0.0 便于容器外访问
CMD ["sh", "-c", "serve -s dist -l tcp://0.0.0.0:${PORT:-8080} -n"]
